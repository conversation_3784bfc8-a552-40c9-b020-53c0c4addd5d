# CRA - Code Review Assistant

A CLI tool that runs pylint and flake8 on files or directories and saves the combined output to a markdown file.
Optionally generates an LLM summary of the linting results.

## Installation

```bash
pip install .
```

Or for development:

```bash
pip install -e .
```

## Usage

```bash
# Lint a single file
cra path/to/file.py

# Lint an entire directory
cra path/to/directory/

# Specify a custom output file
cra path/to/file.py -o custom_report.md

# Generate LLM summary (requires environment variables)
cra path/to/file.py --llm-summary
```

## Features

- Runs both pylint and flake8 on the specified file or directory
- Generates a markdown report with the output of both tools
- Saves reports to a "Reports" directory by default with unique filenames
- Customizable output location with the `-o` option
- Optional LLM summary generation with `--llm-summary` flag

## LLM Summarization

To generate an LLM summary of the linting results, you need to set up the following environment variables:

- `OPENAI_BASE_URL`: The base URL of your OpenAI compatible endpoint
- `OPENAI_API_KEY`: Your API key for the OpenAI compatible endpoint
- `OPENAI_MODEL`: (Optional) The model to use (defaults to "gemini-pro")

Example `.env` file:
```env
OPENAI_BASE_URL=https://api.gemini.com/v1
OPENAI_API_KEY=your_api_key_here
OPENAI_MODEL=gemini-pro
```