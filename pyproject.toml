[project]
name = "cra"
version = "0.1.0"
description = "A CLI tool that runs pylint, flake8, and bandit on files or directories and saves the combined output to a markdown file"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "click>=8.2.1",
    "flake8>=7.3.0",
    "pylint>=3.3.8",
    "python-dotenv>=1.0.0",
    "openai>=1.0.0",
    "bandit>=1.8.6",
    "vulture>=2.14",
    "radon>=6.0.1",
    "rich>=14.1.0",
    "langchain>=0.3.27",
    "langchain-community>=0.3.29",
    "langchain-text-splitters>=0.3.11",
    "langchain-google-genai>=2.1.10",
    "chromadb>=1.0.20",
    "langchain-huggingface>=0.3.1",
    "langchain-mistralai>=0.2.11",
    "langchain-cohere>=0.4.5",
    "langchain-core>=0.3.75",
]

[project.scripts]
cra = "cra.main:main"
