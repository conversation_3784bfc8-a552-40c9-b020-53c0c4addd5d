"""
Data processing utilities for handling CSV files and data transformations.
"""

import csv
import json
import pandas as pd
from typing import Dict, List, Any, Optional
from pathlib import Path


class DataProcessor:
    """Handles data processing operations for various file formats."""
    
    def __init__(self):
        self.data_cache = {}
        self.processed_files = []
    
    def read_csv(self, file_path: str, delimiter: str = ',') -> List[Dict[str, Any]]:
        """Read CSV file and return list of dictionaries."""
        data = []
        try:
            with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile, delimiter=delimiter)
                for row in reader:
                    data.append(dict(row))
            
            self.processed_files.append(file_path)
            return data
        except FileNotFoundError:
            print(f"File not found: {file_path}")
            return []
        except Exception as e:
            print(f"Error reading CSV: {e}")
            return []
    
    def write_csv(self, data: List[Dict[str, Any]], file_path: str, delimiter: str = ',') -> bool:
        """Write data to CSV file."""
        if not data:
            return False
        
        try:
            fieldnames = data[0].keys()
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames, delimiter=delimiter)
                writer.writeheader()
                writer.writerows(data)
            return True
        except Exception as e:
            print(f"Error writing CSV: {e}")
            return False
    
    def filter_data(self, data: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter data based on given criteria."""
        filtered_data = []
        for row in data:
            match = True
            for key, value in filters.items():
                if key not in row or row[key] != value:
                    match = False
                    break
            if match:
                filtered_data.append(row)
        return filtered_data
    
    def aggregate_data(self, data: List[Dict[str, Any]], group_by: str, 
                      aggregate_field: str, operation: str = 'sum') -> Dict[str, float]:
        """Aggregate data by grouping and performing operations."""
        groups = {}
        
        for row in data:
            group_key = row.get(group_by)
            if group_key is None:
                continue
            
            if group_key not in groups:
                groups[group_key] = []
            
            try:
                value = float(row.get(aggregate_field, 0))
                groups[group_key].append(value)
            except (ValueError, TypeError):
                continue
        
        result = {}
        for group, values in groups.items():
            if operation == 'sum':
                result[group] = sum(values)
            elif operation == 'avg':
                result[group] = sum(values) / len(values) if values else 0
            elif operation == 'count':
                result[group] = len(values)
            elif operation == 'max':
                result[group] = max(values) if values else 0
            elif operation == 'min':
                result[group] = min(values) if values else 0
        
        return result
    
    def transform_data(self, data: List[Dict[str, Any]], 
                      transformations: Dict[str, callable]) -> List[Dict[str, Any]]:
        """Apply transformations to data fields."""
        transformed_data = []
        
        for row in data.copy():
            new_row = row.copy()
            for field, transform_func in transformations.items():
                if field in new_row:
                    try:
                        new_row[field] = transform_func(new_row[field])
                    except Exception as e:
                        print(f"Error transforming field {field}: {e}")
            transformed_data.append(new_row)
        
        return transformed_data
    
    def export_to_json(self, data: List[Dict[str, Any]], file_path: str) -> bool:
        """Export data to JSON file."""
        try:
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error exporting to JSON: {e}")
            return False
    
    def get_data_summary(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get summary statistics of the data."""
        if not data:
            return {}
        
        summary = {
            'total_rows': len(data),
            'columns': list(data[0].keys()) if data else [],
            'column_count': len(data[0]) if data else 0
        }
        
        # Analyze each column
        column_analysis = {}
        for column in summary['columns']:
            values = [row.get(column) for row in data if row.get(column) is not None]
            column_analysis[column] = {
                'non_null_count': len(values),
                'null_count': len(data) - len(values),
                'unique_values': len(set(str(v) for v in values))
            }
        
        summary['column_analysis'] = column_analysis
        return summary


def create_sample_data() -> List[Dict[str, Any]]:
    """Create sample data for testing."""
    return [
        {'name': 'Alice', 'age': 25, 'department': 'Engineering', 'salary': 75000},
        {'name': 'Bob', 'age': 30, 'department': 'Marketing', 'salary': 65000},
        {'name': 'Charlie', 'age': 35, 'department': 'Engineering', 'salary': 85000},
        {'name': 'Diana', 'age': 28, 'department': 'Sales', 'salary': 60000},
        {'name': 'Eve', 'age': 32, 'department': 'Engineering', 'salary': 90000},
    ]


def main():
    """Example usage of DataProcessor."""
    processor = DataProcessor()
    
    # Create and process sample data
    sample_data = create_sample_data()
    
    # Filter data
    engineers = processor.filter_data(sample_data, {'department': 'Engineering'})
    print(f"Engineers: {len(engineers)}")
    
    # Aggregate data
    dept_salaries = processor.aggregate_data(sample_data, 'department', 'salary', 'avg')
    print("Average salaries by department:")
    for dept, avg_salary in dept_salaries.items():
        print(f"  {dept}: ${avg_salary:,.2f}")
    
    # Get summary
    summary = processor.get_data_summary(sample_data)
    print(f"Data summary: {summary['total_rows']} rows, {summary['column_count']} columns")


if __name__ == "__main__":
    main()
