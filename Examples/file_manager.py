"""
File management utilities for handling file operations and directory management.
"""

import os
import shutil
import json
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Generator
from datetime import datetime


class FileManager:
    """Handles file and directory operations with logging and error handling."""
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path).resolve()
        self.operation_log = []
        self.supported_extensions = {
            'text': ['.txt', '.md', '.rst'],
            'code': ['.py', '.js', '.html', '.css', '.json', '.xml'],
            'data': ['.csv', '.xlsx', '.json', '.yaml', '.yml'],
            'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp'],
            'document': ['.pdf', '.doc', '.docx', '.ppt', '.pptx']
        }
    
    def _log_operation(self, operation: str, path: str, success: bool, details: str = ""):
        """Log file operations for tracking."""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'operation': operation,
            'path': path,
            'success': success,
            'details': details
        }
        self.operation_log.append(log_entry)
    
    def create_directory(self, dir_path: str, parents: bool = True) -> bool:
        """Create directory with optional parent creation."""
        try:
            full_path = self.base_path / dir_path
            full_path.mkdir(parents=parents, exist_ok=True)
            self._log_operation('create_directory', str(full_path), True)
            return True
        except Exception as e:
            self._log_operation('create_directory', str(full_path), False, str(e))
            return False
    
    def copy_file(self, source: str, destination: str) -> bool:
        """Copy file from source to destination."""
        try:
            src_path = self.base_path / source
            dst_path = self.base_path / destination
            
            # Create destination directory if it doesn't exist
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(src_path, dst_path)
            self._log_operation('copy_file', f"{src_path} -> {dst_path}", True)
            return True
        except Exception as e:
            self._log_operation('copy_file', f"{source} -> {destination}", False, str(e))
            return False
    
    def move_file(self, source: str, destination: str) -> bool:
        """Move file from source to destination."""
        try:
            src_path = self.base_path / source
            dst_path = self.base_path / destination
            
            # Create destination directory if it doesn't exist
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(str(src_path), str(dst_path))
            self._log_operation('move_file', f"{src_path} -> {dst_path}", True)
            return True
        except Exception as e:
            self._log_operation('move_file', f"{source} -> {destination}", False, str(e))
            return False
    
    def delete_file(self, file_path: str) -> bool:
        """Delete a file."""
        try:
            full_path = self.base_path / file_path
            full_path.unlink()
            self._log_operation('delete_file', str(full_path), True)
            return True
        except Exception as e:
            self._log_operation('delete_file', str(full_path), False, str(e))
            return False
    
    def delete_directory(self, dir_path: str, recursive: bool = False) -> bool:
        """Delete directory, optionally recursive."""
        try:
            full_path = self.base_path / dir_path
            if recursive:
                shutil.rmtree(full_path)
            else:
                full_path.rmdir()
            self._log_operation('delete_directory', str(full_path), True)
            return True
        except Exception as e:
            self._log_operation('delete_directory', str(full_path), False, str(e))
            return False
    
    def list_files(self, directory: str = ".", pattern: str = "*", 
                   recursive: bool = False) -> List[Dict[str, Any]]:
        """List files in directory with metadata."""
        try:
            dir_path = self.base_path / directory
            files = []
            
            if recursive:
                file_paths = dir_path.rglob(pattern)
            else:
                file_paths = dir_path.glob(pattern)
            
            for file_path in file_paths:
                if file_path.is_file():
                    stat = file_path.stat()
                    files.append({
                        'name': file_path.name,
                        'path': str(file_path.relative_to(self.base_path)),
                        'size': stat.st_size,
                        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        'extension': file_path.suffix,
                        'type': self._get_file_type(file_path.suffix)
                    })
            
            self._log_operation('list_files', str(dir_path), True, f"Found {len(files)} files")
            return files
        except Exception as e:
            self._log_operation('list_files', directory, False, str(e))
            return []
    
    def _get_file_type(self, extension: str) -> str:
        """Determine file type based on extension."""
        extension = extension.lower()
        for file_type, extensions in self.supported_extensions.items():
            if extension in extensions:
                return file_type
        return 'other'
    
    def get_file_hash(self, file_path: str, algorithm: str = 'md5') -> Optional[str]:
        """Calculate file hash."""
        try:
            full_path = self.base_path / file_path
            hash_func = getattr(hashlib, algorithm)()
            
            with open(full_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            
            file_hash = hash_func.hexdigest()
            self._log_operation('get_file_hash', str(full_path), True, f"{algorithm}: {file_hash}")
            return file_hash
        except Exception as e:
            self._log_operation('get_file_hash', file_path, False, str(e))
            return None
    
    def find_duplicates(self, directory: str = ".", recursive: bool = True) -> Dict[str, List[str]]:
        """Find duplicate files based on content hash."""
        files = self.list_files(directory, recursive=recursive)
        hash_map = {}
        
        for file_info in files:
            file_hash = self.get_file_hash(file_info['path'])
            if file_hash:
                if file_hash not in hash_map:
                    hash_map[file_hash] = []
                hash_map[file_hash].append(file_info['path'])
        
        # Return only files that have duplicates
        duplicates = {hash_val: paths for hash_val, paths in hash_map.items() if len(paths) > 1}
        self._log_operation('find_duplicates', directory, True, f"Found {len(duplicates)} duplicate groups")
        return duplicates
    
    def get_directory_size(self, directory: str = ".") -> int:
        """Calculate total size of directory."""
        try:
            dir_path = self.base_path / directory
            total_size = 0
            
            for file_path in dir_path.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
            
            self._log_operation('get_directory_size', str(dir_path), True, f"Size: {total_size} bytes")
            return total_size
        except Exception as e:
            self._log_operation('get_directory_size', directory, False, str(e))
            return 0
    
    def cleanup_empty_directories(self, directory: str = ".") -> int:
        """Remove empty directories recursively."""
        try:
            dir_path = self.base_path / directory
            removed_count = 0
            
            # Walk from bottom up to handle nested empty directories
            for root, dirs, files in os.walk(str(dir_path), topdown=False):
                for dir_name in dirs:
                    dir_to_check = Path(root) / dir_name
                    try:
                        if not any(dir_to_check.iterdir()):
                            dir_to_check.rmdir()
                            removed_count += 1
                    except OSError:
                        pass  # Directory not empty or permission error
            
            self._log_operation('cleanup_empty_directories', str(dir_path), True, 
                              f"Removed {removed_count} empty directories")
            return removed_count
        except Exception as e:
            self._log_operation('cleanup_empty_directories', directory, False, str(e))
            return 0
    
    def get_operation_log(self) -> List[Dict[str, Any]]:
        """Get log of all file operations."""
        return self.operation_log.copy()
    
    def export_log(self, log_file: str = "file_operations.json") -> bool:
        """Export operation log to JSON file."""
        try:
            log_path = self.base_path / log_file
            with open(log_path, 'w') as f:
                json.dump(self.operation_log, f, indent=2)
            return True
        except Exception:
            return False


def main():
    """Example usage of FileManager."""
    fm = FileManager()
    
    # Create test directory structure
    fm.create_directory("test_dir/subdir")
    
    # List files in current directory
    files = fm.list_files(".", recursive=True)
    print(f"Found {len(files)} files")
    
    # Get directory size
    size = fm.get_directory_size(".")
    print(f"Directory size: {size:,} bytes")
    
    # Show operation log
    log = fm.get_operation_log()
    print(f"Performed {len(log)} operations")


if __name__ == "__main__":
    main()
