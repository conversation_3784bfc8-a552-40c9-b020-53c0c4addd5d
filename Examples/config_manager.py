"""
Configuration management system for handling application settings and environment variables.
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, asdict
from configparser import ConfigParser


@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    host: str = "localhost"
    port: int = 5432
    database: str = "myapp"
    username: str = "user"
    password: str = ""
    ssl_mode: str = "prefer"
    pool_size: int = 10
    
    def get_connection_string(self) -> str:
        """Generate database connection string."""
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"


@dataclass
class APIConfig:
    """API configuration settings."""
    base_url: str = "https://api.example.com"
    api_key: str = ""
    timeout: int = 30
    max_retries: int = 3
    rate_limit: int = 100
    version: str = "v1"
    
    def get_headers(self) -> Dict[str, str]:
        """Get default API headers."""
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'MyApp/1.0'
        }
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        return headers


@dataclass
class LoggingConfig:
    """Logging configuration settings."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "app.log"
    max_file_size: int = 10485760  # 10MB
    backup_count: int = 5
    console_output: bool = True


@dataclass
class AppConfig:
    """Main application configuration."""
    app_name: str = "MyApplication"
    version: str = "1.0.0"
    debug: bool = False
    secret_key: str = ""
    allowed_hosts: List[str] = None
    database: DatabaseConfig = None
    api: APIConfig = None
    logging: LoggingConfig = None
    
    def __post_init__(self):
        """Initialize nested configurations."""
        if self.database is None:
            self.database = DatabaseConfig()
        if self.api is None:
            self.api = APIConfig()
        if self.logging is None:
            self.logging = LoggingConfig()
        if self.allowed_hosts is None:
            self.allowed_hosts = ["localhost", "127.0.0.1"]


class ConfigManager:
    """Manages application configuration from multiple sources."""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        self.config = AppConfig()
        self.config_sources = []
    
    def load_from_env(self, prefix: str = "APP_") -> 'ConfigManager':
        """Load configuration from environment variables."""
        env_config = {}
        
        for key, value in os.environ.items():
            if key.startswith(prefix):
                config_key = key[len(prefix):].lower()
                
                # Handle nested configurations
                if config_key.startswith('db_'):
                    if 'database' not in env_config:
                        env_config['database'] = {}
                    env_config['database'][config_key[3:]] = self._convert_value(value)
                elif config_key.startswith('api_'):
                    if 'api' not in env_config:
                        env_config['api'] = {}
                    env_config['api'][config_key[4:]] = self._convert_value(value)
                elif config_key.startswith('log_'):
                    if 'logging' not in env_config:
                        env_config['logging'] = {}
                    env_config['logging'][config_key[4:]] = self._convert_value(value)
                else:
                    env_config[config_key] = self._convert_value(value)
        
        self._update_config(env_config)
        self.config_sources.append('environment')
        return self
    
    def load_from_json(self, file_path: str) -> 'ConfigManager':
        """Load configuration from JSON file."""
        try:
            config_file = self.config_dir / file_path
            if config_file.exists():
                with open(config_file, 'r') as f:
                    json_config = json.load(f)
                self._update_config(json_config)
                self.config_sources.append(f'json:{file_path}')
        except Exception as e:
            print(f"Error loading JSON config: {e}")
        return self
    
    def load_from_yaml(self, file_path: str) -> 'ConfigManager':
        """Load configuration from YAML file."""
        try:
            config_file = self.config_dir / file_path
            if config_file.exists():
                with open(config_file, 'r') as f:
                    yaml_config = yaml.safe_load(f)
                self._update_config(yaml_config)
                self.config_sources.append(f'yaml:{file_path}')
        except Exception as e:
            print(f"Error loading YAML config: {e}")
        return self
    
    def load_from_ini(self, file_path: str) -> 'ConfigManager':
        """Load configuration from INI file."""
        try:
            config_file = self.config_dir / file_path
            if config_file.exists():
                parser = ConfigParser()
                parser.read(config_file)
                
                ini_config = {}
                for section_name in parser.sections():
                    section_dict = dict(parser[section_name])
                    
                    # Convert values
                    for key, value in section_dict.items():
                        section_dict[key] = self._convert_value(value)
                    
                    if section_name == 'main':
                        ini_config.update(section_dict)
                    else:
                        ini_config[section_name] = section_dict
                
                self._update_config(ini_config)
                self.config_sources.append(f'ini:{file_path}')
        except Exception as e:
            print(f"Error loading INI config: {e}")
        return self
    
    def _convert_value(self, value: str) -> Union[str, int, float, bool, List[str]]:
        """Convert string value to appropriate type."""
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        if value.isdigit():
            return int(value)
        
        try:
            return float(value)
        except ValueError:
            pass
        
        # Handle comma-separated lists
        if ',' in value:
            return [item.strip() for item in value.split(',')]
        
        return value
    
    def _update_config(self, config_dict: Dict[str, Any]):
        """Update configuration with new values."""
        for key, value in config_dict.items():
            if hasattr(self.config, key):
                if isinstance(value, dict):
                    # Handle nested configurations
                    nested_config = getattr(self.config, key)
                    for nested_key, nested_value in value.items():
                        if hasattr(nested_config, nested_key):
                            setattr(nested_config, nested_key, nested_value)
                else:
                    setattr(self.config, key, value)
    
    def save_to_json(self, file_path: str = "config.json") -> bool:
        """Save current configuration to JSON file."""
        try:
            config_file = self.config_dir / file_path
            config_dict = asdict(self.config)
            with open(config_file, 'w') as f:
                json.dump(config_dict, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving JSON config: {e}")
            return False
    
    def save_to_yaml(self, file_path: str = "config.yaml") -> bool:
        """Save current configuration to YAML file."""
        try:
            config_file = self.config_dir / file_path
            config_dict = asdict(self.config)
            with open(config_file, 'w') as f:
                yaml.dump(config_dict, f, default_flow_style=False, indent=2)
            return True
        except Exception as e:
            print(f"Error saving YAML config: {e}")
            return False
    
    def get_config(self) -> AppConfig:
        """Get current configuration."""
        return self.config
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []
        
        if not self.config.secret_key:
            issues.append("Secret key is not set")
        
        if not self.config.database.password:
            issues.append("Database password is not set")
        
        if not self.config.api.api_key:
            issues.append("API key is not set")
        
        if self.config.database.port < 1 or self.config.database.port > 65535:
            issues.append("Invalid database port")
        
        if self.config.api.timeout <= 0:
            issues.append("API timeout must be positive")
        
        return issues
    
    def get_summary(self) -> Dict[str, Any]:
        """Get configuration summary."""
        return {
            'app_name': self.config.app_name,
            'version': self.config.version,
            'debug': self.config.debug,
            'config_sources': self.config_sources,
            'database_host': self.config.database.host,
            'api_base_url': self.config.api.base_url,
            'logging_level': self.config.logging.level
        }


def main():
    """Example usage of ConfigManager."""
    # Create config manager
    config_manager = ConfigManager()
    
    # Load from multiple sources (order matters - later sources override earlier ones)
    config_manager.load_from_json('default.json').load_from_env('MYAPP_')
    
    # Get configuration
    config = config_manager.get_config()
    print(f"App: {config.app_name} v{config.version}")
    print(f"Database: {config.database.host}:{config.database.port}")
    print(f"API: {config.api.base_url}")
    
    # Validate configuration
    issues = config_manager.validate_config()
    if issues:
        print("Configuration issues:")
        for issue in issues:
            print(f"  - {issue}")
    
    # Save configuration
    config_manager.save_to_json('current_config.json')
    
    # Show summary
    summary = config_manager.get_summary()
    print(f"Config summary: {summary}")


if __name__ == "__main__":
    main()
