"""
User management system with authentication and user data handling.
"""

import hashlib
import json
import os
from datetime import datetime
from typing import Dict, List, Optional


class User:
    """Represents a user in the system."""
    
    def __init__(self, username: str, email: str, created_at: str = None):
        self.username = username
        self.email = email
        self.created_at = created_at or datetime.now().isoformat()
        self.is_active = True
        self.last_login = None
    
    def to_dict(self) -> Dict:
        """Convert user to dictionary."""
        return {
            'username': self.username,
            'email': self.email,
            'created_at': self.created_at,
            'is_active': self.is_active,
            'last_login': self.last_login
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'User':
        """Create user from dictionary."""
        user = cls(data['username'], data['email'], data['created_at'])
        user.is_active = data.get('is_active', True)
        user.last_login = data.get('last_login')
        return user


class UserManager:
    """Manages user operations including authentication and data persistence."""
    
    def __init__(self, data_file: str = "users.json"):
        self.data_file = data_file
        self.users: Dict[str, User] = {}
        self.passwords: Dict[str, str] = {}
        self.load_users()
    
    def hash_password(self, password: str) -> str:
        """Hash password using SHA-256."""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def create_user(self, username: str, email: str, password: str) -> bool:
        """Create a new user."""
        if username in self.users:
            return False
        
        user = User(username, email)
        self.users[username] = user
        self.passwords[username] = self.hash_password(password)
        self.save_users()
        return True
    
    def authenticate(self, username: str, password: str) -> bool:
        """Authenticate user with username and password."""
        if username not in self.users:
            return False
        
        hashed_password = self.hash_password(password)
        if self.passwords[username] == hashed_password:
            self.users[username].last_login = datetime.now().isoformat()
            self.save_users()
            return True
        return False
    
    def get_user(self, username: str) -> Optional[User]:
        """Get user by username."""
        return self.users.get(username)
    
    def list_users(self) -> List[User]:
        """Get list of all users."""
        return list(self.users.values())
    
    def deactivate_user(self, username: str) -> bool:
        """Deactivate a user."""
        if username in self.users:
            self.users[username].is_active = False
            self.save_users()
            return True
        return False
    
    def save_users(self):
        """Save users to file."""
        data = {
            'users': {username: user.to_dict() for username, user in self.users.items()},
            'passwords': self.passwords
        }
        with open(self.data_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_users(self):
        """Load users from file."""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                
                self.passwords = data.get('passwords', {})
                users_data = data.get('users', {})
                
                for username, user_data in users_data.items():
                    self.users[username] = User.from_dict(user_data)
            except (json.JSONDecodeError, KeyError):
                self.users = {}
                self.passwords = {}


def main():
    """Example usage of UserManager."""
    manager = UserManager()
    
    # Create some test users
    manager.create_user("alice", "<EMAIL>", "password123")
    manager.create_user("bob", "<EMAIL>", "secret456")
    
    # Test authentication
    if manager.authenticate("alice", "password123"):
        print("Alice authenticated successfully")
    
    # List all users
    users = manager.list_users()
    print(f"Total users: {len(users)}")
    for user in users:
        print(f"- {user.username} ({user.email})")


if __name__ == "__main__":
    main()
