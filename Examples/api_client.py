"""
HTTP API client for making requests and handling responses.
"""

import json
import time
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin, urlparse
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class APIClient:
    """HTTP client for API interactions with retry logic and error handling."""
    
    def __init__(self, base_url: str, timeout: int = 30, max_retries: int = 3):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'APIClient/1.0'
        }
        
        # Setup retry strategy
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        self.request_history = []
    
    def set_auth_token(self, token: str):
        """Set authentication token for requests."""
        self.headers['Authorization'] = f'Bearer {token}'
        self.session.headers.update(self.headers)
    
    def set_api_key(self, api_key: str, header_name: str = 'X-API-Key'):
        """Set API key for requests."""
        self.headers[header_name] = api_key
        self.session.headers.update(self.headers)
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make HTTP request with error handling."""
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))
        
        # Log request
        request_info = {
            'method': method,
            'url': url,
            'timestamp': time.time(),
            'headers': dict(self.session.headers)
        }
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            request_info['status_code'] = response.status_code
            request_info['response_time'] = time.time() - request_info['timestamp']
            self.request_history.append(request_info)
            
            response.raise_for_status()
            return response
            
        except requests.exceptions.RequestException as e:
            request_info['error'] = str(e)
            self.request_history.append(request_info)
            raise
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make GET request."""
        response = self._make_request('GET', endpoint, params=params)
        return response.json() if response.content else {}
    
    def post(self, endpoint: str, data: Optional[Dict] = None, 
             json_data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make POST request."""
        kwargs = {}
        if json_data:
            kwargs['json'] = json_data
        elif data:
            kwargs['data'] = data
        
        response = self._make_request('POST', endpoint, **kwargs)
        return response.json() if response.content else {}
    
    def put(self, endpoint: str, data: Optional[Dict] = None,
            json_data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make PUT request."""
        kwargs = {}
        if json_data:
            kwargs['json'] = json_data
        elif data:
            kwargs['data'] = data
        
        response = self._make_request('PUT', endpoint, **kwargs)
        return response.json() if response.content else {}
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        """Make DELETE request."""
        response = self._make_request('DELETE', endpoint)
        return response.json() if response.content else {}
    
    def patch(self, endpoint: str, data: Optional[Dict] = None,
              json_data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make PATCH request."""
        kwargs = {}
        if json_data:
            kwargs['json'] = json_data
        elif data:
            kwargs['data'] = data
        
        response = self._make_request('PATCH', endpoint, **kwargs)
        return response.json() if response.content else {}
    
    def upload_file(self, endpoint: str, file_path: str, 
                   field_name: str = 'file') -> Dict[str, Any]:
        """Upload file to endpoint."""
        with open(file_path, 'rb') as f:
            files = {field_name: f}
            # Remove Content-Type header for file uploads
            headers = {k: v for k, v in self.headers.items() 
                      if k.lower() != 'content-type'}
            response = self._make_request('POST', endpoint, files=files, headers=headers)
        return response.json() if response.content else {}
    
    def get_request_history(self) -> List[Dict[str, Any]]:
        """Get history of all requests made."""
        return self.request_history.copy()
    
    def clear_history(self):
        """Clear request history."""
        self.request_history.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about API usage."""
        if not self.request_history:
            return {}
        
        total_requests = len(self.request_history)
        successful_requests = len([r for r in self.request_history 
                                 if 'error' not in r and r.get('status_code', 0) < 400])
        
        response_times = [r.get('response_time', 0) for r in self.request_history 
                         if 'response_time' in r]
        
        return {
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'success_rate': successful_requests / total_requests if total_requests > 0 else 0,
            'avg_response_time': sum(response_times) / len(response_times) if response_times else 0,
            'max_response_time': max(response_times) if response_times else 0,
            'min_response_time': min(response_times) if response_times else 0
        }


class RESTAPIClient(APIClient):
    """Extended API client with common REST operations."""
    
    def list_resources(self, resource: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """List all resources of a given type."""
        response = self.get(f'/{resource}', params=params)
        return response.get('data', response) if isinstance(response, dict) else response
    
    def get_resource(self, resource: str, resource_id: str) -> Dict[str, Any]:
        """Get a specific resource by ID."""
        return self.get(f'/{resource}/{resource_id}')
    
    def create_resource(self, resource: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new resource."""
        return self.post(f'/{resource}', json_data=data)
    
    def update_resource(self, resource: str, resource_id: str, 
                       data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing resource."""
        return self.put(f'/{resource}/{resource_id}', json_data=data)
    
    def delete_resource(self, resource: str, resource_id: str) -> Dict[str, Any]:
        """Delete a resource."""
        return self.delete(f'/{resource}/{resource_id}')


def main():
    """Example usage of APIClient."""
    # Example with a mock API
    client = RESTAPIClient('https://jsonplaceholder.typicode.com')
    
    try:
        # List posts
        posts = client.list_resources('posts')
        print(f"Found {len(posts)} posts")
        
        # Get specific post
        if posts:
            first_post = client.get_resource('posts', '1')
            print(f"First post title: {first_post.get('title', 'N/A')}")
        
        # Get API usage stats
        stats = client.get_stats()
        print(f"API Stats: {stats}")
        
    except Exception as e:
        print(f"API Error: {e}")


if __name__ == "__main__":
    main()
