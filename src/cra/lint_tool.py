import subprocess
import os
import json
from datetime import datetime
from pathlib import Path
from rich.console import Console
from rich.syntax import Syntax
from rich.panel import Panel
from rich.table import Table
from rich.text import Text


def run_linter(command, path):
    """Run linter command and return output."""
    try:
        result = subprocess.run(
            command + [path],
            capture_output=True,
            text=True,
            timeout=30
        )
        return result.stdout if result.returncode == 0 or result.stdout else result.stderr
    except Exception as e:
        return f"Error running {' '.join(command)}: {str(e)}"


def run_vulture(path):
    """Run vulture to detect dead code."""
    try:
        result = subprocess.run(
            ['vulture', path, '--min-confidence', '80'],
            capture_output=True,
            text=True,
            timeout=30
        )
        return result.stdout if result.stdout else "No dead code detected."
    except Exception as e:
        return f"Error running vulture: {str(e)}"


def run_radon_complexity(path):
    """Run radon to analyze code complexity."""
    try:
        result = subprocess.run(
            ['radon', 'cc', path, '-s'],
            capture_output=True,
            text=True,
            timeout=30
        )
        return result.stdout if result.stdout else "No complexity issues found."
    except Exception as e:
        return f"Error running radon complexity: {str(e)}"


def run_radon_maintainability(path):
    """Run radon to analyze maintainability index."""
    try:
        result = subprocess.run(
            ['radon', 'mi', path, '-s'],
            capture_output=True,
            text=True,
            timeout=30
        )
        return result.stdout if result.stdout else "No maintainability issues found."
    except Exception as e:
        return f"Error running radon maintainability: {str(e)}"


def run_linters(file_path):
    """Run all linters and return formatted markdown report."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Run existing linters
    pylint_output = run_linter(['pylint'], file_path)
    flake8_output = run_linter(['flake8'], file_path)
    bandit_output = run_linter(['bandit', '-r'], file_path)

    # Run new linters
    vulture_output = run_vulture(file_path)
    radon_complexity_output = run_radon_complexity(file_path)
    radon_maintainability_output = run_radon_maintainability(file_path)

    return f"""# Linting Report

- **Path**: `{file_path}`
- **Generated on**: {timestamp}

## Pylint Output

```
{pylint_output or "No output or errors occurred."}
```

## Flake8 Output

```
{flake8_output or "No output or errors occurred."}
```

## Bandit Output

```
{bandit_output or "No output or errors occurred."}
```

## Vulture Output (Dead Code Detection)

```
{vulture_output or "No dead code detected."}
```

## Radon Complexity Analysis

```
{radon_complexity_output or "No complexity issues found."}
```

## Radon Maintainability Index

```
{radon_maintainability_output or "No maintainability issues found."}
```

---
*Report generated by CRA*
"""


def get_git_diff():
    """Get git diff for uncommitted changes."""
    try:
        # Get staged changes
        staged_result = subprocess.run(
            ['git', 'diff', '--cached'],
            capture_output=True,
            text=True,
            timeout=30
        )

        # Get unstaged changes
        unstaged_result = subprocess.run(
            ['git', 'diff'],
            capture_output=True,
            text=True,
            timeout=30
        )

        diff_output = ""
        if staged_result.stdout:
            diff_output += "## Staged Changes\n\n```diff\n" + staged_result.stdout + "\n```\n\n"

        if unstaged_result.stdout:
            diff_output += "## Unstaged Changes\n\n```diff\n" + unstaged_result.stdout + "\n```\n\n"

        if not diff_output:
            diff_output = "No uncommitted changes found.\n"

        return diff_output
    except Exception as e:
        return f"Error getting git diff: {str(e)}\n"


def display_git_diff_rich():
    """Display git diff with rich formatting."""
    console = Console()

    try:
        # Get staged changes
        staged_result = subprocess.run(
            ['git', 'diff', '--cached'],
            capture_output=True,
            text=True,
            timeout=30
        )

        # Get unstaged changes
        unstaged_result = subprocess.run(
            ['git', 'diff'],
            capture_output=True,
            text=True,
            timeout=30
        )

        if staged_result.stdout:
            console.print(Panel(
                Syntax(staged_result.stdout, "diff", theme="monokai", line_numbers=True),
                title="[bold green]Staged Changes[/bold green]",
                border_style="green"
            ))
            console.print()

        if unstaged_result.stdout:
            console.print(Panel(
                Syntax(unstaged_result.stdout, "diff", theme="monokai", line_numbers=True),
                title="[bold yellow]Unstaged Changes[/bold yellow]",
                border_style="yellow"
            ))
            console.print()

        if not staged_result.stdout and not unstaged_result.stdout:
            console.print(Panel(
                "[green]No uncommitted changes found.[/green]",
                title="[bold blue]Git Status[/bold blue]",
                border_style="blue"
            ))

    except Exception as e:
        console.print(Panel(
            f"[red]Error getting git diff: {str(e)}[/red]",
            title="[bold red]Error[/bold red]",
            border_style="red"
        ))


def generate_recommendations_from_linting(report_content):
    """Generate recommendations based on linting results."""
    recommendations = []

    # Analyze pylint output for common issues
    if "C0103" in report_content:  # Invalid name
        recommendations.append("Consider using more descriptive variable names following PEP 8 conventions")

    if "W0613" in report_content:  # Unused argument
        recommendations.append("Remove unused function arguments or prefix with underscore if intentionally unused")

    if "R0903" in report_content:  # Too few public methods
        recommendations.append("Consider if this class should be a function or dataclass instead")

    if "C0301" in report_content:  # Line too long
        recommendations.append("Break long lines to improve readability (max 79-88 characters)")

    # Analyze vulture output
    if "unused" in report_content.lower() and "vulture" in report_content.lower():
        recommendations.append("Remove dead code identified by Vulture to improve maintainability")

    # Analyze radon complexity
    if "F" in report_content and "radon" in report_content.lower():
        recommendations.append("Refactor complex functions (F-rated) by breaking them into smaller functions")

    if "D" in report_content and "radon" in report_content.lower():
        recommendations.append("Consider refactoring D-rated functions to reduce complexity")

    # Analyze bandit security issues
    if "HIGH" in report_content and "bandit" in report_content.lower():
        recommendations.append("Address high-severity security issues identified by Bandit immediately")

    if not recommendations:
        recommendations.append("Great job! No major issues found in the linting analysis.")

    return recommendations


def save_report(report, output_file):
    """Save markdown report to file."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)

