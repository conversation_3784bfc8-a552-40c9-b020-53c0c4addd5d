import subprocess
from datetime import datetime


def run_linter(command, path):
    """Run linter command and return output."""
    try:
        result = subprocess.run(
            command + [path],
            capture_output=True,
            text=True,
            timeout=30
        )
        return result.stdout if result.returncode == 0 or result.stdout else result.stderr
    except Exception as e:
        return f"Error running {' '.join(command)}: {str(e)}"


def run_linters(file_path):
    """Run all linters and return formatted markdown report."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    pylint_output = run_linter(['pylint'], file_path)
    flake8_output = run_linter(['flake8'], file_path)
    bandit_output = run_linter(['bandit', '-r'], file_path)

    return f"""# Linting Report

- **Path**: `{file_path}`
- **Generated on**: {timestamp}

## Pylint Output

```
{pylint_output or "No output or errors occurred."}
```

## Flake8 Output

```
{flake8_output or "No output or errors occurred."}
```

## Bandit Output

```
{bandit_output or "No output or errors occurred."}
```

---
*Report generated by CRA*
"""


def save_report(report, output_file):
    """Save markdown report to file."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)

