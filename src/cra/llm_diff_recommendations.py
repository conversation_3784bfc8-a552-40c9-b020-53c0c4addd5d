"""LLM-powered diff recommendations using Gemini with structured output."""

import os
from typing import List, Optional
from pydantic import BaseModel, Field
from langchain_core.prompts import ChatPromptTemplate
from langchain_google_genai import ChatGoogleGenerativeAI
from rich.console import Console
from rich.columns import Columns
from rich.panel import Panel
from rich.syntax import Syntax
from rich.text import Text
from .config import settings


class CodeChange(BaseModel):
    """Schema for a single code change recommendation."""
    
    file_path: str = Field(description="Path to the file being changed")
    line_number: int = Field(description="Line number where change should be made")
    original_code: str = Field(description="Original code that should be changed")
    recommended_code: str = Field(description="Recommended replacement code")
    reason: str = Field(description="Explanation for why this change is recommended")
    issue_type: str = Field(description="Type of issue being fixed (e.g., 'pylint', 'flake8', 'complexity', 'security')")


class DiffRecommendations(BaseModel):
    """Schema for all diff-style recommendations."""
    
    summary: str = Field(description="Brief summary of all recommended changes")
    changes: List[CodeChange] = Field(description="List of specific code changes")
    priority_order: List[str] = Field(description="List of issue types in order of priority (highest first)")


def get_file_content(file_path: str) -> str:
    """Get the content of a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        return f"Error reading file: {str(e)}"


def generate_mock_recommendations(linting_report: str, file_path: str) -> DiffRecommendations:
    """Generate mock recommendations for demo purposes when API key is not available."""
    changes = []
    priority_order = []

    # Always generate some sample recommendations for demo
    # Parse common issues from the linting report
    if "C0303" in linting_report:  # Trailing whitespace
        changes.append(CodeChange(
            file_path=file_path,
            line_number=24,
            original_code="    def example_method(self):    ",
            recommended_code="    def example_method(self):",
            reason="Remove trailing whitespace to improve code cleanliness",
            issue_type="style"
        ))
        if "style" not in priority_order:
            priority_order.append("style")

    if "C0301" in linting_report or "E501" in linting_report:  # Line too long
        changes.append(CodeChange(
            file_path=file_path,
            line_number=27,
            original_code='        config_value = "This is a very long configuration string that exceeds the recommended line length"',
            recommended_code='        config_value = (\n            "This is a very long configuration string that exceeds "\n            "the recommended line length"\n        )',
            reason="Break long line to improve readability (max 79-88 characters)",
            issue_type="style"
        ))
        if "style" not in priority_order:
            priority_order.append("style")

    if "unused" in linting_report.lower():  # Vulture findings
        changes.append(CodeChange(
            file_path=file_path,
            line_number=9,
            original_code="from typing import Optional",
            recommended_code="# from typing import Optional  # Removed unused import",
            reason="Remove unused import identified by Vulture",
            issue_type="dead_code"
        ))
        if "dead_code" not in priority_order:
            priority_order.append("dead_code")

    if "W0613" in linting_report:  # Unused argument
        changes.append(CodeChange(
            file_path=file_path,
            line_number=45,
            original_code="    def process_data(self, data, unused_param):",
            recommended_code="    def process_data(self, data, _unused_param):",
            reason="Prefix unused parameter with underscore to indicate intentional non-use",
            issue_type="style"
        ))
        if "style" not in priority_order:
            priority_order.append("style")

    # If no specific issues found, generate some general recommendations
    if not changes:
        changes.extend([
            CodeChange(
                file_path=file_path,
                line_number=1,
                original_code="# Example: Remove trailing whitespace",
                recommended_code="# Example: Remove trailing whitespace",
                reason="General code cleanup recommendation",
                issue_type="style"
            ),
            CodeChange(
                file_path=file_path,
                line_number=2,
                original_code="# Example: Break long lines",
                recommended_code="# Example: Break long lines",
                reason="Improve code readability",
                issue_type="style"
            )
        ])

    # Set default priority order if empty
    if not priority_order:
        priority_order = ["style", "dead_code", "complexity", "security"]

    summary = f"Generated {len(changes)} mock recommendations based on linting patterns. Set GOOGLE_API_KEY for real LLM analysis."

    return DiffRecommendations(
        summary=summary,
        changes=changes,
        priority_order=priority_order
    )


def generate_llm_diff_recommendations(linting_report: str, file_path: str) -> DiffRecommendations:
    """Generate LLM-powered diff recommendations using Gemini with structured output."""

    # Check if Google API key is available
    if not os.getenv('GOOGLE_API_KEY'):
        result = generate_mock_recommendations(linting_report, file_path)
        return result

    # Get the actual file content
    file_content = get_file_content(file_path)

    # Create the prompt template
    prompt = ChatPromptTemplate.from_messages([
        ("system", """You are an expert Python code reviewer and refactoring assistant.

        Your task is to analyze a linting report and the corresponding source code, then provide specific,
        actionable code changes in a structured format.

        Guidelines:
        1. Focus on the most impactful issues first (security > complexity > style)
        2. Provide exact line-by-line changes with before/after code
        3. Only suggest changes that directly address issues mentioned in the linting report
        4. Keep original code formatting and style as much as possible
        5. Provide clear explanations for each change
        6. Group related changes logically

        Priority order (highest to lowest):
        - Security issues (bandit)
        - High complexity functions (radon)
        - Dead code (vulture)
        - Code style issues (pylint, flake8)
        """),
        ("user", """Please analyze this linting report and source code, then provide specific code change recommendations.

Linting Report:
{linting_report}

Source Code File: {file_path}
```python
{file_content}
```

Provide structured recommendations with exact before/after code changes that address the issues found in the linting report.""")
    ])

    try:
        # Initialize Gemini LLM
        llm = ChatGoogleGenerativeAI(
            model=settings.gemini_model,
            temperature=0.1,
            max_tokens=4000
        )

        # Create the chain with structured output
        chain = prompt | llm.with_structured_output(DiffRecommendations)

        # Generate recommendations
        result = chain.invoke({
            "linting_report": linting_report,
            "file_path": file_path,
            "file_content": file_content
        })

        # Ensure we return a valid DiffRecommendations object
        if isinstance(result, DiffRecommendations):
            return result
        else:
            # Fallback if LLM returns unexpected format
            return DiffRecommendations(
                summary="LLM returned unexpected format, using fallback",
                changes=[],
                priority_order=[]
            )
    except Exception as e:
        # Return a fallback response if LLM fails
        return DiffRecommendations(
            summary=f"Error generating recommendations: {str(e)}",
            changes=[],
            priority_order=[]
        )


def format_change_as_diff(change: CodeChange) -> str:
    """Format a single code change as a unified diff."""
    diff_lines = []
    diff_lines.append(f"@@ -{change.line_number},1 +{change.line_number},1 @@")
    diff_lines.append(f"-{change.original_code}")
    diff_lines.append(f"+{change.recommended_code}")
    diff_lines.append(f"# {change.reason}")
    diff_lines.append("")
    
    return "\n".join(diff_lines)


def create_full_diff_view(recommendations: DiffRecommendations, file_path: str) -> str:
    """Create a full diff-style view of all recommendations."""
    if not recommendations.changes:
        return "No specific code changes recommended."
    
    diff_content = []
    diff_content.append(f"--- a/{file_path}")
    diff_content.append(f"+++ b/{file_path}")
    diff_content.append("")
    
    # Group changes by issue type for better organization
    for issue_type in recommendations.priority_order:
        type_changes = [c for c in recommendations.changes if c.issue_type == issue_type]
        if type_changes:
            diff_content.append(f"# {issue_type.upper()} FIXES")
            diff_content.append("")
            for change in type_changes:
                diff_content.append(format_change_as_diff(change))
    
    return "\n".join(diff_content)


def display_side_by_side_diff(current_diff: str, recommended_diff: str, recommendations: DiffRecommendations):
    """Display current git diff and LLM recommendations side by side using Rich."""
    console = Console()
    
    # Create the current diff panel
    if current_diff.strip():
        current_panel = Panel(
            Syntax(current_diff, "diff", theme="monokai", line_numbers=True),
            title="[bold blue]Current Git Diff[/bold blue]",
            border_style="blue",
            padding=(1, 2)
        )
    else:
        current_panel = Panel(
            "[dim]No current git changes[/dim]",
            title="[bold blue]Current Git Diff[/bold blue]",
            border_style="blue",
            padding=(1, 2)
        )
    
    # Create the recommendations panel
    if recommended_diff.strip() and recommendations.changes:
        # Create content with proper Rich formatting
        from rich.text import Text
        from rich.console import Group

        rec_content = []
        rec_content.append(Text(f"Summary: {recommendations.summary}", style="bold green"))
        rec_content.append(Text(""))
        rec_content.append(Text(f"Priority Order: {' → '.join(recommendations.priority_order)}", style="bold yellow"))
        rec_content.append(Text(""))
        rec_content.append(Text("Recommended Changes:", style="bold cyan"))
        rec_content.append(Text(""))

        # Add syntax-highlighted diff
        rec_content.append(Syntax(recommended_diff, "diff", theme="monokai", line_numbers=True))

        recommended_panel = Panel(
            Group(*rec_content),
            title="[bold green]LLM Recommended Changes[/bold green]",
            border_style="green",
            padding=(1, 2)
        )
    else:
        recommended_panel = Panel(
            "[dim]No specific recommendations generated[/dim]",
            title="[bold green]LLM Recommended Changes[/bold green]",
            border_style="green",
            padding=(1, 2)
        )
    
    # Display side by side
    console.print("\n")
    console.print("[bold magenta]Side-by-Side Diff Comparison[/bold magenta]")
    console.print("=" * 80)
    console.print()
    
    columns = Columns([current_panel, recommended_panel], equal=True, expand=True)
    console.print(columns)
    
    # Display detailed change explanations
    if recommendations.changes:
        console.print("\n")
        console.print("[bold cyan]Detailed Change Explanations:[/bold cyan]")
        console.print("-" * 50)
        
        for i, change in enumerate(recommendations.changes, 1):
            console.print(f"\n[bold]{i}. {change.issue_type.upper()} - Line {change.line_number}[/bold]")
            console.print(f"[yellow]Reason:[/yellow] {change.reason}")
            console.print(f"[red]- {change.original_code}[/red]")
            console.print(f"[green]+ {change.recommended_code}[/green]")


def generate_and_display_llm_diff(linting_report: str, file_path: str, current_git_diff: str = ""):
    """Main function to generate and display LLM diff recommendations."""
    console = Console()

    console.print("[bold yellow]Generating LLM-powered diff recommendations...[/bold yellow]")

    try:
        # Generate recommendations using Gemini
        recommendations = generate_llm_diff_recommendations(linting_report, file_path)

        # Debug prints
        console.print(f"[dim]Debug: Generated {len(recommendations.changes) if recommendations else 0} recommendations[/dim]")

        # Ensure we have a valid recommendations object
        if recommendations is None:
            recommendations = DiffRecommendations(
                summary="Failed to generate recommendations",
                changes=[],
                priority_order=[]
            )

        # Create diff-style view
        recommended_diff = create_full_diff_view(recommendations, file_path)

        # Debug prints
        console.print(f"[dim]Debug: Diff length: {len(recommended_diff)}, Has changes: {bool(recommendations.changes)}[/dim]")

        # Display side by side
        display_side_by_side_diff(current_git_diff, recommended_diff, recommendations)

        return recommendations, recommended_diff

    except Exception as e:
        console.print(f"[red]Error generating LLM diff recommendations: {str(e)}[/red]")
        fallback_recommendations = DiffRecommendations(
            summary=f"Error: {str(e)}",
            changes=[],
            priority_order=[]
        )
        return fallback_recommendations, ""
