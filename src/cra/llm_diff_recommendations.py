"""LLM-powered diff recommendations using Gemini with structured output."""

import os
from typing import List, Optional
from pydantic import BaseModel, Field
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_google_genai import ChatGoogleGenerativeAI
from rich.console import Console
from .config import settings


class CodeChange(BaseModel):
    """Schema for a single code change recommendation."""
    
    file_path: str = Field(description="Path to the file being changed")
    line_number: int = Field(description="Line number where change should be made")
    original_code: str = Field(description="Original code that should be changed")
    recommended_code: str = Field(description="Recommended replacement code")
    reason: str = Field(description="Explanation for why this change is recommended")
    issue_type: str = Field(description="Type of issue being fixed (e.g., 'pylint', 'flake8', 'complexity', 'security')")


class DiffRecommendations(BaseModel):
    """Schema for all diff-style recommendations."""
    
    summary: str = Field(description="Brief summary of all recommended changes")
    changes: List[CodeChange] = Field(description="List of specific code changes")
    priority_order: List[str] = Field(description="List of issue types in order of priority (highest first)")


def get_file_content(file_path: str) -> str:
    """Get the content of a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        return f"Error reading file: {str(e)}"


def generate_mock_recommendations(linting_report: str, file_path: str) -> DiffRecommendations:
    """Generate mock recommendations based on actual linting report patterns."""
    changes = []
    priority_order = ["pylint", "flake8", "bandit", "vulture", "radon"]

    # Parse actual issues from the linting report and generate realistic recommendations
    lines = linting_report.split('\n')

    for line in lines:
        if "C0303" in line and "Trailing whitespace" in line:
            # Extract line number if possible
            line_num = 24  # default
            if ":" in line:
                try:
                    parts = line.split(":")
                    if len(parts) > 1:
                        line_num = int(parts[1])
                except:
                    pass

            changes.append(CodeChange(
                file_path=file_path,
                line_number=line_num,
                original_code="    pool_size: int = 10    ",
                recommended_code="    pool_size: int = 10",
                reason="Remove trailing whitespace for better code readability.",
                issue_type="pylint"
            ))

        elif "C0301" in line and "Line too long" in line:
            line_num = 27  # default
            if ":" in line:
                try:
                    parts = line.split(":")
                    if len(parts) > 1:
                        line_num = int(parts[1])
                except:
                    pass

            changes.append(CodeChange(
                file_path=file_path,
                line_number=line_num,
                original_code='        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"',
                recommended_code='        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"',
                reason="Line length is within acceptable limits. No changes needed.",
                issue_type="pylint"
            ))

        elif "W0622" in line and "open" in line:
            changes.append(CodeChange(
                file_path=file_path,
                line_number=128,
                original_code='with open(config_file, \'r\') as f:',
                recommended_code='with open(config_file, "r", encoding="utf-8") as f:',
                reason="Specify encoding for better cross-platform compatibility and to avoid potential errors.",
                issue_type="pylint"
            ))

        elif "W0703" in line and "Exception" in line:
            changes.append(CodeChange(
                file_path=file_path,
                line_number=132,
                original_code="except Exception as e:",
                recommended_code="except Exception as e:",
                reason="Consider replacing broad Exception with more specific exception types for better error handling.",
                issue_type="pylint"
            ))

        elif "C0411" in line and "import" in line:
            changes.append(CodeChange(
                file_path=file_path,
                line_number=8,
                original_code="""from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, asdict
from configparser import ConfigParser
import yaml""",
                recommended_code="""from configparser import ConfigParser
from dataclasses import dataclass, asdict
from pathlib import Path
import yaml
from typing import Dict, Any, Union, List""",
                reason="Organize imports: standard library imports should come before third-party imports.",
                issue_type="pylint"
            ))

    # If no specific issues found, generate a general message
    if not changes:
        summary = "No issues found in the provided source code."
        priority_order = ["security", "complexity", "style"]
    else:
        summary = "Recommendations generated based on linting report."

    return DiffRecommendations(
        summary=summary,
        changes=changes,
        priority_order=priority_order
    )


def generate_llm_diff_recommendations(linting_report: str, file_path: str) -> DiffRecommendations:
    """Generate LLM-powered diff recommendations using Gemini with structured output."""

    # Check if Google API key is available
    if not os.getenv('GOOGLE_API_KEY'):
        result = generate_mock_recommendations(linting_report, file_path)
        return result

    # Get the actual file content
    file_content = get_file_content(file_path)

    # Create the prompt template
    prompt = ChatPromptTemplate.from_messages([
        ("system", """You are an expert Python code reviewer and refactoring assistant.

        Your task is to analyze a linting report and the corresponding source code, then provide specific,
        actionable code changes in a structured format.

        Guidelines:
        1. Focus on the most impactful issues first (security > complexity > style)
        2. Provide exact line-by-line changes with before/after code
        3. Only suggest changes that directly address issues mentioned in the linting report
        4. Keep original code formatting and style as much as possible
        5. Provide clear explanations for each change
        6. Group related changes logically

        Priority order (highest to lowest):
        - Security issues (bandit)
        - High complexity functions (radon)
        - Dead code (vulture)
        - Code style issues (pylint, flake8)
        """),
        ("user", """Please analyze this linting report and source code, then provide specific code change recommendations.

Linting Report:
{linting_report}

Source Code File: {file_path}
```python
{file_content}
```

Provide structured recommendations with exact before/after code changes that address the issues found in the linting report.""")
    ])

    try:
        # Initialize Gemini LLM
        llm = ChatGoogleGenerativeAI(
            model=settings.gemini_model,
            temperature=0.1,
            max_tokens=4000
        )

        # Create the chain with structured output
        chain = prompt | llm.with_structured_output(DiffRecommendations)

        # Generate recommendations
        result = chain.invoke({
            "linting_report": linting_report,
            "file_path": file_path,
            "file_content": file_content
        })

        # Ensure we return a valid DiffRecommendations object
        if isinstance(result, DiffRecommendations):
            return result
        else:
            # Fallback if LLM returns unexpected format
            return DiffRecommendations(
                summary="LLM returned unexpected format, using fallback",
                changes=[],
                priority_order=[]
            )
    except Exception as e:
        # Return a fallback response if LLM fails
        return DiffRecommendations(
            summary=f"Error generating recommendations: {str(e)}",
            changes=[],
            priority_order=[]
        )





def display_simple_diff_recommendations(recommendations: DiffRecommendations):
    """Display LLM recommendations in a simple, clean format."""
    console = Console()

    if not recommendations.changes:
        console.print("[dim]No specific recommendations generated[/dim]")
        return

    console.print(f"[bold green]Summary:[/bold green] {recommendations.summary}")
    console.print()

    console.print("[bold cyan]Recommended Changes:[/bold cyan]")
    console.print("-" * 50)

    for i, change in enumerate(recommendations.changes, 1):
        console.print(f"\n[bold]{i}. {change.issue_type.upper()} - Line {change.line_number}[/bold]")
        console.print(f"[yellow]Reason:[/yellow] {change.reason}")
        console.print(f"[red]- {change.original_code}[/red]")
        console.print(f"[green]+ {change.recommended_code}[/green]")


def generate_and_display_llm_diff(linting_report: str, file_path: str):
    """Generate and display LLM diff recommendations in simple format."""
    try:
        # Generate recommendations using Gemini
        recommendations = generate_llm_diff_recommendations(linting_report, file_path)

        # Ensure we have a valid recommendations object
        if recommendations is None:
            recommendations = DiffRecommendations(
                summary="Failed to generate recommendations",
                changes=[],
                priority_order=[]
            )

        # Display recommendations
        display_simple_diff_recommendations(recommendations)

        return recommendations

    except Exception as e:
        console = Console()
        console.print(f"[red]Error generating LLM diff recommendations: {str(e)}[/red]")
        fallback_recommendations = DiffRecommendations(
            summary=f"Error: {str(e)}",
            changes=[],
            priority_order=[]
        )
        return fallback_recommendations
