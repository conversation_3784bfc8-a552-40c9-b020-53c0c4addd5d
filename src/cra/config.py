import os
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()


class Settings:
    """Application settings and configuration."""
    
    def __init__(self):
        self.codebase_path = os.getenv('CODEBASE_PATH', './')
        self.cache_dir = os.getenv('CACHE_DIR', './.cra_cache1')
        self.reports_dir = os.getenv('REPORTS_DIR', './Reports')
        
        # LLM Configuration
        self.llm_base_url = os.getenv('LLM_BASE_URL')
        self.llm_api_key = os.getenv('LLM_API_KEY')
        self.llm_model = os.getenv('MODEL', 'gemini-2.0-flash')
        
        # Embedding Configuration
        self.embed_model = os.getenv('EMBED_MODEL', 'models/embedding-001')
        self.gemini_model = os.getenv('GEMINI_MODEL', 'gemini-1.5-flash')
        
        # Document Processing
        self.chunk_size = int(os.getenv('CHUNK_SIZE', '800'))
        self.chunk_overlap = int(os.getenv('CHUNK_OVERLAP', '120'))
        
        # Ensure directories exist
        Path(self.cache_dir).mkdir(exist_ok=True)
        Path(self.reports_dir).mkdir(exist_ok=True)
    
    @property
    def latest_report_path(self):
        """Path to the latest report file."""
        return os.path.join(self.reports_dir, '.latest_report.md')


settings = Settings()
