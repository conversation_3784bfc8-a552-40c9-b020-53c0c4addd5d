import os
import uuid
from pathlib import Path

import click

from . import run_linters, save_report, CodebaseChat, generate_llm_summary, settings
from .lint_tool import get_git_diff, display_git_diff_rich, generate_recommendations_from_linting


@click.group()
def cli():
    """CRA - Code Review Assistant"""
    pass


@cli.command()
@click.argument('path', type=click.Path(exists=True))
@click.option('-o', '--output', default=None, help='Output markdown file path')
@click.option('--llm-summary', is_flag=True, default=True, help='Generate LLM summary')
@click.option('--show-diff', is_flag=True, default=False, help='Show git diff with rich formatting')
@click.option('--include-diff', is_flag=True, default=True, help='Include git diff in report')
@click.option('--recommendations', is_flag=True, default=True, help='Generate recommendations')
def lint(path, output, llm_summary, show_diff, include_diff, recommendations):
    """Run linters on specified file or directory."""
    abs_path = os.path.abspath(path)

    if output is None:
        Path(settings.reports_dir).mkdir(exist_ok=True)
        base_name = os.path.basename(abs_path.rstrip(os.sep))
        unique_id = str(uuid.uuid4())[:8]
        output = os.path.join(settings.reports_dir, f"{base_name}_{unique_id}.md")

    click.echo(f"Running linters on: {abs_path}")

    report_content = run_linters(abs_path)

    # Add git diff to report if requested
    if include_diff:
        click.echo("Getting git diff...")
        git_diff = get_git_diff()
        report_content += f"\n\n## Git Diff\n\n{git_diff}"

    # Generate recommendations
    if recommendations:
        click.echo("Generating recommendations...")
        recs = generate_recommendations_from_linting(report_content)
        recommendations_text = "\n".join([f"- {rec}" for rec in recs])
        report_content += f"\n\n## Recommendations\n\n{recommendations_text}\n"

    if llm_summary:
        click.echo("Generating LLM summary...")
        summary = generate_llm_summary(report_content)
        report_content += f"\n\n## LLM Summary\n\n{summary}\n"

    save_report(report_content, output)

    # Update latest report
    try:
        with open(output, 'r', encoding='utf-8') as src, \
             open(settings.latest_report_path, 'w', encoding='utf-8') as dst:
            dst.write(src.read())
        click.echo(f"Latest report updated: {settings.latest_report_path}")
    except Exception as e:
        click.echo(f"Warning: Could not update latest report: {e}")

    # Show rich diff display if requested
    if show_diff:
        click.echo("\n" + "="*50)
        click.echo("Git Diff (Rich Display)")
        click.echo("="*50)
        display_git_diff_rich()

    click.echo(f"\nReport saved to: {output}")


@cli.command()
def diff():
    """Show git diff with rich formatting."""
    click.echo("Git Diff (Rich Display)")
    click.echo("="*50)
    display_git_diff_rich()


@cli.command()
@click.argument('codebase_path', type=click.Path(exists=True), default='.')
@click.option('-q', '--question', help='Question to ask about the codebase')
@click.option('--reindex', is_flag=True, help='Force re-indexing of the codebase')
def chat(codebase_path, question, reindex):
    """Chat with your codebase using AI."""
    chat_system = CodebaseChat(codebase_path=codebase_path)

    if reindex:
        click.echo("Re-indexing codebase...")
        try:
            chat_system.initialize()
        except RuntimeError as e:
            if "quota" in str(e).lower():
                click.echo(f"Error: {e}")
                return
            else:
                raise
    else:
        click.echo(f"Checking for existing index at: {chat_system.persist_dir}")
        if not chat_system.load_existing_index():
            click.echo("No existing index found. Creating new index...")
            try:
                chat_system.initialize()
            except RuntimeError as e:
                if "quota" in str(e).lower():
                    click.echo(f"Error: {e}")
                    return
                else:
                    raise

    # Start interactive chat mode
    click.echo("Interactive chat mode. Type 'quit' to exit.")

    # If initial question provided, answer it first
    if question:
        answer = chat_system.ask(question)
        click.echo("Answer:")
        click.echo(answer)
        click.echo()  # Add blank line

    # Continue with interactive loop
    while True:
        try:
            user_question = click.prompt("Ask a question about the codebase")
            if user_question.lower() in ['quit', 'exit', 'q']:
                break
            answer = chat_system.ask(user_question)
            click.echo("Answer:")
            click.echo(answer)
            click.echo()  # Add blank line for readability
        except (KeyboardInterrupt, EOFError):
            click.echo("\nGoodbye!")
            break


def main():
    cli()


if __name__ == "__main__":
    main()
