#!/usr/bin/env python3
"""
A simple test file with some intentional issues for linting.
"""

import os, sys  # Multiple imports on one line - flake8 issue

def bad_function( ):
    x=5 # No space around operator - flake8 issue
    if x>3: # No space around operator - flake8 issue
        print("x is greater than 3")
    else:
        print("x is not greater than 3")
    
    # Unused variable - pylint issue
    unused_var = "This is unused"
    
    return x

# Line too long - flake8 issue
this_is_a_very_long_line_that_exceeds_the_maximum_line_length_allowed_by_flake8_and_should_trigger_a_warning_when_we_run_the_linter_on_this_file = "test"

if __name__ == "__main__":
    bad_function()